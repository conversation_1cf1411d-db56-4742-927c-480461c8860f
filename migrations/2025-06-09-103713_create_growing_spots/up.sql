CREATE TABLE growing_spots (
    id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
    growing_area_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    position_x REAL NOT NULL,
    position_y REAL NOT NULL,
    width REAL NOT NULL,
    height REAL NOT NULL,
    soil_type TEXT,
    sun_exposure TEXT, -- 'full_sun', 'partial_sun', 'partial_shade', 'full_shade'
    water_access TEXT, -- 'excellent', 'good', 'poor'
    notes TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (growing_area_id) REFERENCES growing_area_shapes(id)
);
