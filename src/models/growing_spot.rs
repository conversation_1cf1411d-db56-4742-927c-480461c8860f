use diesel::prelude::*;
use serde::{Serialize, Deserialize};
use chrono::NaiveDateTime;

#[derive(Queryable, Selectable, Serialize, Deserialize)]
#[diesel(table_name = crate::schema::growing_spots)]
#[diesel(check_for_backend(diesel::sqlite::Sqlite))]
pub struct GrowingSpot {
    pub id: i32,
    pub growing_area_id: i32,
    pub name: String,
    pub position_x: f32,
    pub position_y: f32,
    pub width: f32,
    pub height: f32,
    pub soil_type: Option<String>,
    pub sun_exposure: Option<String>,
    pub water_access: Option<String>,
    pub notes: Option<String>,
    pub is_active: Option<bool>,
    pub created_at: Option<NaiveDateTime>,
    pub updated_at: Option<NaiveDateTime>,
}

#[derive(Insertable)]
#[diesel(table_name = crate::schema::growing_spots)]
pub struct NewGrowingSpot<'a> {
    pub growing_area_id: i32,
    pub name: &'a str,
    pub position_x: f32,
    pub position_y: f32,
    pub width: f32,
    pub height: f32,
    pub soil_type: Option<&'a str>,
    pub sun_exposure: Option<&'a str>,
    pub water_access: Option<&'a str>,
    pub notes: Option<&'a str>,
    pub is_active: Option<bool>,
}

impl GrowingSpot {
    pub fn find_all(conn: &mut SqliteConnection) -> QueryResult<Vec<GrowingSpot>> {
        use crate::schema::growing_spots::dsl::*;
        growing_spots.load::<GrowingSpot>(conn)
    }

    pub fn find_by_id(conn: &mut SqliteConnection, spot_id: i32) -> QueryResult<Option<GrowingSpot>> {
        use crate::schema::growing_spots::dsl::*;
        growing_spots
            .filter(id.eq(spot_id))
            .first(conn)
            .optional()
    }

    pub fn find_by_growing_area(conn: &mut SqliteConnection, area_id: i32) -> QueryResult<Vec<GrowingSpot>> {
        use crate::schema::growing_spots::dsl::*;
        growing_spots
            .filter(growing_area_id.eq(area_id))
            .filter(is_active.eq(true))
            .load::<GrowingSpot>(conn)
    }

    pub fn create(conn: &mut SqliteConnection, new_spot: &NewGrowingSpot) -> QueryResult<i32> {
        use crate::schema::growing_spots::dsl::*;

        diesel::insert_into(growing_spots)
            .values(new_spot)
            .execute(conn)?;

        // Get the last inserted row ID
        let last_id: i64 = diesel::select(diesel::dsl::sql::<diesel::sql_types::BigInt>("last_insert_rowid()"))
            .get_result(conn)?;

        Ok(last_id as i32)
    }

    pub fn update(conn: &mut SqliteConnection, spot_id: i32, updated_spot: &NewGrowingSpot) -> QueryResult<usize> {
        use crate::schema::growing_spots::dsl::*;
        
        diesel::update(growing_spots.filter(id.eq(spot_id)))
            .set((
                name.eq(updated_spot.name),
                position_x.eq(updated_spot.position_x),
                position_y.eq(updated_spot.position_y),
                width.eq(updated_spot.width),
                height.eq(updated_spot.height),
                soil_type.eq(updated_spot.soil_type),
                sun_exposure.eq(updated_spot.sun_exposure),
                water_access.eq(updated_spot.water_access),
                notes.eq(updated_spot.notes),
                is_active.eq(updated_spot.is_active),
            ))
            .execute(conn)
    }

    pub fn delete(conn: &mut SqliteConnection, spot_id: i32) -> QueryResult<usize> {
        use crate::schema::growing_spots::dsl::*;
        diesel::delete(growing_spots.filter(id.eq(spot_id)))
            .execute(conn)
    }

    pub fn deactivate(conn: &mut SqliteConnection, spot_id: i32) -> QueryResult<usize> {
        use crate::schema::growing_spots::dsl::*;
        diesel::update(growing_spots.filter(id.eq(spot_id)))
            .set(is_active.eq(false))
            .execute(conn)
    }
}
