/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'class', // Enable dark mode via a class
  content: [
    './templates/**/*.html',
    './src/**/*.rs',
    './static/js/**/*.js',
  ],
  theme: {
    extend: {
      colors: {
        sage: {
          50: '#f6f7f6',
          100: '#e3e7e3',
          200: '#c7d0c7',
          300: '#a3b3a3',
          400: '#7a8f7a',
          500: '#5c735c',
          600: '#485a48',
          700: '#3c4a3c',
          800: '#323d32',
          900: '#2a332a',
          950: '#161b16',
        },
        // Material 3 inspired sage-tinted pastels
        primary: {
          50: '#f0f4f0',
          100: '#dce6dc',
          200: '#b9ceb9',
          300: '#94b394',
          400: '#729872',
          500: '#5a7e5a',
          600: '#486548',
          700: '#3a523a',
          800: '#2f422f',
          900: '#283628',
          950: '#141c14',
        },
        secondary: {
          50: '#f4f6f4',
          100: '#e6ebe6',
          200: '#ced8ce',
          300: '#b0c0b0',
          400: '#8fa48f',
          500: '#748974',
          600: '#5d6f5d',
          700: '#4c5a4c',
          800: '#404a40',
          900: '#373f37',
          950: '#1c211c',
        },
        tertiary: {
          50: '#f2f6f2',
          100: '#e0eae0',
          200: '#c2d6c2',
          300: '#9ebb9e',
          400: '#759d75',
          500: '#5a825a',
          600: '#466846',
          700: '#39543a',
          800: '#304530',
          900: '#293a29',
          950: '#141e14',
        },
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      borderRadius: {
        'material': '12px',
        'material-lg': '16px',
        'material-xl': '24px',
      },
      boxShadow: {
        'material-1': '0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15)',
        'material-2': '0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15)',
        'material-3': '0px 1px 3px 0px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15)',
        'material-4': '0px 2px 3px 0px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15)',
        'material-5': '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
      },
    },
  },
  plugins: [],
};
