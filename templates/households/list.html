{% extends "base.html" %}
{% block title %}My Households{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-sage-900 dark:text-sage-100">My Households</h1>
        <a href="/households/new" class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-material shadow-material-2 transition-all duration-200 hover:shadow-material-3">
            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Create New Household
        </a>
    </div>

    {% if households %}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for household in households %}
        <div class="bg-white dark:bg-sage-800 rounded-material shadow-material-2 p-6 hover:shadow-material-3 transition-all duration-200 border border-sage-200 dark:border-sage-700">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-sage-900 dark:text-sage-100">{{ household.name }}</h2>
                {% if household.is_owner %}
                    <span class="bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 text-xs font-medium px-3 py-1 rounded-full">Owner</span>
                {% else %}
                    <span class="bg-secondary-100 dark:bg-secondary-900 text-secondary-800 dark:text-secondary-200 text-xs font-medium px-3 py-1 rounded-full">{{ household.user_role | title }}</span>
                {% endif %}
            </div>

            <div class="mb-4">
                <span class="text-sm text-sage-600 dark:text-sage-400">
                    {{ household.member_count }} member{% if household.member_count != 1 %}s{% endif %}
                </span>
            </div>

            <div class="flex items-center justify-between space-x-2">
                <form method="post" action="/households/{{ household.id }}/switch" class="inline">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                    <button type="submit" class="bg-tertiary-500 hover:bg-tertiary-600 text-white px-4 py-2 rounded-material text-sm transition-colors">
                        Switch To
                    </button>
                </form>
                <a href="/households/{{ household.id }}/view" class="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium text-sm">
                    View Details →
                </a>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="text-center py-12">
        <div class="bg-sage-100 dark:bg-sage-900 rounded-full w-24 h-24 mx-auto flex items-center justify-center mb-6">
            <svg class="w-12 h-12 text-sage-400 dark:text-sage-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
        </div>
        <h3 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-2">No households yet</h3>
        <p class="text-sage-600 dark:text-sage-400 mb-8 max-w-md mx-auto">Get started by creating your first household to organize your garden properties and share them with others.</p>
        <a href="/households/new" class="inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-material shadow-material-2 hover:shadow-material-3 transition-all duration-200">
            <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Create Your First Household
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}
